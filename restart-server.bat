@echo off
title Restart Vertex Server
color 0C

echo ========================================
echo Restarting Vertex Server
echo ========================================
echo.

echo Stopping any existing server processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im nodemon.exe >nul 2>&1

echo Waiting for processes to stop...
timeout /t 3 /nobreak >nul

echo.
echo Starting backend server with fixes...
cd server
start "Vertex Backend - Fixed" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo Server Restarted!
echo ========================================
echo.
echo The MongoDB connection error should be fixed now.
echo Check the backend terminal for:
echo ✅ "Connected to MongoDB successfully"
echo ✅ "Server is running on port 3000"
echo.
echo If you still see errors, try:
echo 1. Make sure MongoDB is running
echo 2. Run: setup-database.bat
echo.
echo Press any key to close...
pause >nul
