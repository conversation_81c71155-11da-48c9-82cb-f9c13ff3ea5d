import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { getCurrentUser, isAuthenticated } from '@/lib/auth';
import { UserRole } from '@/types';
import { throttle } from '@/utils/throttle';

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuth, setIsAuth] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const refreshUserInternal = async () => {
    // Prevent multiple simultaneous calls
    if (isLoading && hasInitialized) {
      return;
    }

    try {
      setIsLoading(true);

      // Check authentication first
      const authenticated = await isAuthenticated();
      console.log('Authentication check result:', authenticated);
      setIsAuth(authenticated);

      if (authenticated) {
        const userData = getCurrentUser();
        console.log('User data from getCurrentUser:', userData);
        if (userData) {
          const userObj = {
            id: userData.id || '',
            name: userData.name || 'User',
            email: userData.email || '',
            role: userData.role || 'guest'
          };
          console.log('Setting user object:', userObj);
          setUser(userObj);
        } else {
          console.log('No user data found, setting user to null');
          setUser(null);
        }
      } else {
        console.log('Not authenticated, setting user to null');
        setUser(null);
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
      setUser(null);
      setIsAuth(false);
    } finally {
      setIsLoading(false);
      setHasInitialized(true);
    }
  };

  // Throttle the refresh function to prevent excessive calls
  const refreshUser = useCallback(
    throttle(refreshUserInternal, 1000), // Throttle to max once per second
    [isLoading, hasInitialized]
  );

  useEffect(() => {
    // Only run once on mount
    if (!hasInitialized) {
      refreshUser();
    }
  }, [hasInitialized]);

  // Listen for storage changes (when login happens)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' || e.key === 'jwt_token') {
        console.log('Auth token changed, refreshing user...');
        refreshUser();
      }
    };

    // Also listen for custom events
    const handleAuthChange = () => {
      console.log('Auth change event received, refreshing user...');
      refreshUser();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('authChange', handleAuthChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authChange', handleAuthChange);
    };
  }, [refreshUser]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: isAuth,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
