import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { getCurrentUser, isAuthenticated } from '@/lib/auth';
import { UserRole } from '@/types';
import { throttle } from '@/utils/throttle';

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuth, setIsAuth] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const refreshUserInternal = async () => {
    // Prevent multiple simultaneous calls
    if (isLoading && hasInitialized) {
      return;
    }

    try {
      setIsLoading(true);

      // Check authentication first
      const authenticated = await isAuthenticated();
      setIsAuth(authenticated);

      if (authenticated) {
        const userData = getCurrentUser();
        if (userData) {
          setUser({
            id: userData.id || '',
            name: userData.name || 'User',
            email: userData.email || '',
            role: userData.role || 'guest'
          });
        } else {
          setUser(null);
        }
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
      setUser(null);
      setIsAuth(false);
    } finally {
      setIsLoading(false);
      setHasInitialized(true);
    }
  };

  // Throttle the refresh function to prevent excessive calls
  const refreshUser = useCallback(
    throttle(refreshUserInternal, 1000), // Throttle to max once per second
    [isLoading, hasInitialized]
  );

  useEffect(() => {
    // Only run once on mount
    if (!hasInitialized) {
      refreshUser();
    }
  }, [hasInitialized]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: isAuth,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
