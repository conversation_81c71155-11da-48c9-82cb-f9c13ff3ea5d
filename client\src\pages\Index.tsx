
import { getCurrentUser } from "@/lib/auth";
import { useNavigate } from "react-router-dom";
import { useEffect, useRef } from "react";
import ThemeToggle from "@/components/common/ThemeToggle";
import LoginForm from "@/components/auth/LoginForm";

export default function Index() {
  const navigate = useNavigate();
  const user = getCurrentUser();
  const hasNavigated = useRef(false);

  useEffect(() => {
    // Prevent multiple navigation attempts
    if (user && !hasNavigated.current) {
      hasNavigated.current = true;
      navigate("/dashboard", { replace: true });
    }
  }, [user, navigate]);

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <header className="border-b border-border py-4 px-6 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
            V
          </div>
          <span className="text-xl font-semibold ml-2">Vertex</span>
        </div>
        <ThemeToggle />
      </header>
      
      <main className="flex-grow flex items-center justify-center p-6">
        <div className="max-w-md w-full mx-auto space-y-8">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold tracking-tight">Welcome to Vertex</h1>
            <p className="text-muted-foreground">
              Sign in to access the school management system
            </p>
          </div>
          
          <LoginForm />
        </div>
      </main>
      
      <footer className="border-t border-border py-4 px-6 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} Vertex Education. All rights reserved.</p>
      </footer>
    </div>
  );
}
