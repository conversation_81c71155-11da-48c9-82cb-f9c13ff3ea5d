import { UserRole } from '@/types';

// Simple token generation
export async function encrypt(payload: any): Promise<string> {
  // Simple base64 encoding instead of JWT
  return btoa(JSON.stringify({
    ...payload,
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 // 7 days
  }));
}

// Decode the token
export async function decrypt(token: string): Promise<any> {
  try {
    const payload = JSON.parse(atob(token));
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      console.error("Token expired");
      return null;
    }
    
    return payload;
  } catch (error) {
    console.error("Token decoding failed:", error);
    return null;
  }
}

// Store auth token in localStorage
export async function setAuthCookies(payload: any): Promise<void> {
  const encryptedUserData = await encrypt(payload);
  localStorage.setItem('auth_token', encryptedUserData);

  // Store the actual JWT token separately if provided
  if (payload.token) {
    localStorage.setItem('jwt_token', payload.token);
  }
}

// Get auth token from localStorage
export function getAuthCookies(): string | undefined {
  return localStorage.getItem('auth_token') || undefined;
}

// Get current user from token
export async function getUser(): Promise<any | null> {
  const token = getAuthCookies();
  if (token) {
    const user = await decrypt(token);
    return user;
  }
  return null;
}

// Check if user is authenticated
export async function isAuthenticated(): Promise<boolean> {
  try {
    console.log('isAuthenticated: Checking authentication...');
    const token = getAuthCookies();
    console.log('isAuthenticated: Token found:', !!token);
    if (!token) return false;

    const payload = await decrypt(token);
    console.log('isAuthenticated: Payload after decrypt:', payload);

    // If token is expired or invalid, clean up and return false
    if (!payload) {
      console.log('isAuthenticated: No payload, logging out');
      logoutUser();
      return false;
    }

    console.log('isAuthenticated: Authentication successful');
    return true;
  } catch (error) {
    console.error('Authentication check failed:', error);
    logoutUser();
    return false;
  }
}

// Get current user data - SYNCHRONOUS VERSION that doesn't need await
export function getCurrentUser(): any | null {
  const token = getAuthCookies();
  if (!token) return null;

  try {
    // Attempt to decode the token synchronously
    const userData = JSON.parse(atob(token));

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (userData.exp && userData.exp < now) {
      console.warn("Token expired, logging out");
      logoutUser();
      return null;
    }

    return userData;
  } catch (error) {
    console.error('Error getting current user:', error);
    logoutUser();
    return null;
  }
}

// Remove auth token from localStorage
export function logoutUser(): void {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('jwt_token');

  // Trigger auth change event
  window.dispatchEvent(new Event('authChange'));
}

// Login user function (real API call)
export async function loginUser(email: string, password: string): Promise<boolean> {
  try {
    console.log('Attempting login with:', email);

    // Call the real backend API
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username: email, password }),
    });

    console.log('Login response status:', response.status);

    if (!response.ok) {
      console.error('Login failed:', response.status);
      return false;
    }

    const data = await response.json();
    console.log('Login response data:', data);

    if (data.success && data.token && data.user) {
      // Create user session with real data from backend
      const sessionUser = {
        id: data.user.id || data.user._id,
        name: data.user.name || data.user.username,
        email: data.user.email || data.user.username,
        role: data.user.role,
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours from now
      };

      console.log('Storing user session:', sessionUser);

      // Store JWT token
      localStorage.setItem('jwt_token', data.token);

      // Store user data as base64 encoded token
      const userToken = btoa(JSON.stringify(sessionUser));
      localStorage.setItem('auth_token', userToken);

      // Trigger auth change event
      window.dispatchEvent(new Event('authChange'));

      console.log('Login successful, tokens stored');
      return true;
    }

    console.error('Invalid response format:', data);
    return false;
  } catch (error) {
    console.error('Login error:', error);
    return false;
  }
}

// Add the Student role to the roleHierarchy
export const roleHierarchy: Record<UserRole, number> = {
  superAdmin: 50,
  manager: 40,
  secretary: 30,
  teacher: 20,
  student: 10,
  guest: 0
};

// Check if user has permission based on role
export function hasPermission(requiredRoles: UserRole[]): boolean {
  // This is a synchronous function but getCurrentUser is async,
  // so we need a workaround for now
  const storedToken = getAuthCookies();
  if (!storedToken) return false;
  
  try {
    // Attempt to decode the token synchronously
    const userData = JSON.parse(atob(storedToken));
    const userRole = userData.role as UserRole || 'guest';
    const userRoleLevel = roleHierarchy[userRole];
    
    for (const requiredRole of requiredRoles) {
      const requiredRoleLevel = roleHierarchy[requiredRole];
      if (userRoleLevel >= requiredRoleLevel) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return false;
  }
}

// Synchronous function to check role
export function hasRole(role: UserRole | UserRole[]): boolean {
  const roles = Array.isArray(role) ? role : [role];
  return hasPermission(roles);
}

// Function for system-related functions that were previously referenced
export function getSystemStats() {
  // Mock implementation
  return {
    systemHealth: 'healthy',
    uptime: '3 days, 5 hours',
    errorRate: 0.02,
    averageResponseTime: 125,
    recentErrors: 2,
    pendingUpdates: 3,
    systemLoad: {
      cpu: 35,
      memory: 42,
      disk: 56,
      network: 28
    },
    activeUsers: 12,
    lastBackup: new Date().toISOString()
  };
}

export function triggerSystemBackup() {
  // Mock implementation
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true, message: 'Backup completed successfully' });
    }, 2000);
  });
}

// Mock function for permissions related to notes
export function hasNotesPermission(action: string): boolean {
  // This is a simplified version
  const storedToken = getAuthCookies();
  if (!storedToken) return false;
  
  try {
    // Attempt to decode the token synchronously
    const userData = JSON.parse(atob(storedToken));
    const role = userData.role as UserRole || 'guest';
    
    // SuperAdmin, Manager and Secretary can perform all actions
    if (['superAdmin', 'manager', 'secretary'].includes(role)) {
      return true;
    }
    // Teachers can view and create notes
    else if (role === 'teacher' && ['view', 'create'].includes(action)) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking notes permission:', error);
    return false;
  }
}

// Mock function for user management
export function createUser(userData: any) {
  // Mock implementation
  console.log('Creating user:', userData);
  return Promise.resolve({ success: true, user: { id: 'new-user-id', ...userData } });
}

export function updateUserStatus(userId: string, status: string) {
  // Mock implementation
  console.log(`Updating user ${userId} status to ${status}`);
  return Promise.resolve({ success: true });
}

export function updateUserRole(userId: string, role: UserRole) {
  // Mock implementation
  console.log(`Updating user ${userId} role to ${role}`);
  return Promise.resolve({ success: true });
}

export function bulkUpdateUserStatus(userIds: string[], status: string) {
  // Mock implementation
  console.log(`Updating status to ${status} for users:`, userIds);
  return Promise.resolve({ success: true });
}

export function bulkUpdateUserRole(userIds: string[], role: UserRole) {
  // Mock implementation
  console.log(`Updating role to ${role} for users:`, userIds);
  return Promise.resolve({ success: true });
}

export function getSystemLogs() {
  // Mock implementation
  return Promise.resolve({
    logs: [
      { id: '1', timestamp: new Date().toISOString(), severity: 'info', category: 'system', action: 'login', performedBy: 'admin', details: 'System started', status: 'resolved' },
      { id: '2', timestamp: new Date().toISOString(), severity: 'warning', category: 'performance', action: 'alert', performedBy: 'system', details: 'High CPU usage detected', status: 'unresolved' },
      { id: '3', timestamp: new Date().toISOString(), severity: 'error', category: 'database', action: 'connect', performedBy: 'system', details: 'Failed to connect to database', status: 'unresolved' }
    ],
    pagination: {
      totalPages: 1,
      page: 1,
      pageSize: 10,
      total: 3
    }
  });
}
