import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { loginUser } from "@/lib/auth";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";

const LoginForm = () => {
  const [email, setEmail] = useState("admin@local"); // Pre-fill for testing
  const [password, setPassword] = useState("admin"); // Pre-fill for testing
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      console.log('Login form submitted');

      // Pass email as username since backend expects username field
      const success = await loginUser(email, password);
      console.log('Login result:', success);

      if (success) {
        console.log('Login successful, navigating to dashboard');
        toast({
          title: "Login Successful",
          description: "Welcome to Vertex!",
          variant: "default",
        });

        // Small delay to ensure token is stored, then navigate
        setTimeout(() => {
          navigate("/dashboard");
        }, 100);
      } else {
        console.log('Login failed');
        toast({
          title: "Login Failed",
          description: "Invalid email or password",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      toast({
        title: "Login Error",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4 bg-card border rounded-lg p-6 shadow-sm">
      <form onSubmit={handleLogin} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium">
            Email
          </label>
          <Input 
            id="email"
            type="email" 
            placeholder="Enter your email" 
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="password" className="block text-sm font-medium">
              Password
            </label>
            <a 
              href="/reset-password" 
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
            >
              Forgot password?
            </a>
          </div>
          <Input 
            id="password"
            type="password" 
            placeholder="Enter your password" 
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="rememberMe" 
            checked={rememberMe} 
            onCheckedChange={(checked) => setRememberMe(checked as boolean)} 
          />
          <label 
            htmlFor="rememberMe" 
            className="text-sm text-muted-foreground cursor-pointer"
          >
            Remember me
          </label>
        </div>
        
        <Button 
          type="submit" 
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? "Signing in..." : "Sign in"}
        </Button>
      </form>
      
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">Or</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-3">
        <Button 
          variant="outline" 
          type="button"
          onClick={() => {
            toast({
              title: "Demo mode",
              description: "This feature would connect to an OAuth provider in production.",
            });
          }}
        >
          Continue with Google
        </Button>
      </div>
    </div>
  );
};

export default LoginForm;
