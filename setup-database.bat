@echo off
title Database Setup for Vertex
color 0B

echo ========================================
echo Vertex Database Setup
echo ========================================
echo.

echo Step 1: Checking MongoDB status...
sc query "MongoDB" >nul 2>&1
if %errorlevel% == 0 (
    echo MongoDB service found. Checking if it's running...
    sc query "MongoDB" | find "RUNNING" >nul
    if %errorlevel% == 0 (
        echo ✅ MongoDB is already running.
    ) else (
        echo Starting MongoDB service...
        net start MongoDB
        if %errorlevel% == 0 (
            echo ✅ MongoDB service started successfully.
        ) else (
            echo ❌ Failed to start MongoDB service.
            echo Please start MongoDB manually or install it.
            pause
            exit /b 1
        )
    )
) else (
    echo ❌ MongoDB service not found.
    echo Please install MongoDB or start it manually.
    echo.
    echo You can also use MongoDB Atlas (cloud):
    echo 1. Create account at https://www.mongodb.com/atlas
    echo 2. Get connection string
    echo 3. Update MONGODB_URI in server/.env
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Seeding database with initial users...
cd server
npm run seed

if %errorlevel% == 0 (
    echo ✅ Database seeded successfully!
) else (
    echo ❌ Database seeding failed.
    echo This might be because:
    echo - MongoDB is not running
    echo - Users already exist
    echo - Connection issues
)

cd ..

echo.
echo ========================================
echo Database Setup Complete!
echo ========================================
echo.
echo You can now login with:
echo Username: admin@local
echo Password: admin
echo.
echo Press any key to continue...
pause >nul
