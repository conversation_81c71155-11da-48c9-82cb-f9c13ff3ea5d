
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AuthProvider } from "@/contexts/AuthContext";
import ErrorBoundary from "@/components/ErrorBoundary";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Students from "./pages/Students";
import StudentManagement from "./pages/StudentManagement";
import StudentDetail from "./pages/StudentDetail";
import StudentCreate from "./pages/StudentCreate";
import StudentTransfer from "./pages/StudentTransfer";
import StudentPayment from "./pages/StudentPayment";
import PaymentManagement from "./pages/PaymentManagement";
import PaymentDetail from "./pages/PaymentDetail";
import PaymentCreate from "./pages/PaymentCreate";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import Users from "./pages/Users";
import ActivityLog from "./pages/ActivityLog";
import Notes from "./pages/Notes";
import Attendance from "./pages/Attendance";
import Classes from "./pages/Classes";
import ClassDetails from "./pages/ClassDetails";
import ClassCreate from "./pages/ClassCreate";
import ClassEdit from "./pages/ClassEdit";
import TeacherScheduling from "./pages/TeacherScheduling";
import RoomManagement from "./pages/RoomManagement";
import RoomDetail from "./pages/RoomDetail";
import RoomCreate from "./pages/RoomCreate";
import RoomEdit from "./pages/RoomEdit";
import ResetPassword from "./pages/ResetPassword";
import Schedule from "./pages/Schedule";

// Import Reports Pages
import ReportManagement from "./pages/ReportManagement";
import ReportCreate from "./pages/ReportCreate";
import ReportView from "./pages/ReportView";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      refetchInterval: false,
      refetchIntervalInBackground: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <ThemeProvider>
      <AuthProvider>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<Navigate to="/" replace />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/students" element={<StudentManagement />} />
                <Route path="/students/:id" element={<StudentDetail />} />
                <Route path="/students/create" element={<StudentCreate />} />
                <Route path="/students/:id/transfer" element={<StudentTransfer />} />
                <Route path="/students/:id/payment" element={<StudentPayment />} />
                <Route path="/payments" element={<PaymentManagement />} />
                <Route path="/payments/:id" element={<PaymentDetail />} />
                <Route path="/payments/new" element={<PaymentCreate />} />
                <Route path="/payments/student/:studentId" element={<PaymentCreate />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/users" element={<Users />} />
                <Route path="/activity-log" element={<ActivityLog />} />
                <Route path="/notes" element={<Notes />} />
                <Route path="/attendance" element={<Attendance />} />
                <Route path="/classes" element={<Classes />} />
                <Route path="/classes/:id" element={<ClassDetails />} />
                <Route path="/classes/create" element={<ClassCreate />} />
                <Route path="/classes/:id/edit" element={<ClassEdit />} />
                <Route path="/classes/:id/scheduling" element={<TeacherScheduling />} />
                <Route path="/schedule" element={<Schedule />} />
                {/* Room Management Routes */}
                <Route path="/rooms" element={<RoomManagement />} />
                <Route path="/rooms/:id" element={<RoomDetail />} />
                <Route path="/rooms/create" element={<RoomCreate />} />
                <Route path="/rooms/:id/edit" element={<RoomEdit />} />

                {/* Add Reports Routes */}
                <Route path="/reports" element={<ReportManagement />} />
                <Route path="/reports/create" element={<ReportCreate />} />
                <Route path="/reports/view/:id" element={<ReportView />} />

                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </QueryClientProvider>
      </AuthProvider>
    </ThemeProvider>
  </ErrorBoundary>
);

export default App;
