@echo off
echo Checking MongoDB status...

:: Check if MongoDB service is running
sc query "MongoDB" >nul 2>&1
if %errorlevel% == 0 (
    echo MongoDB service found. Checking if it's running...
    sc query "MongoDB" | find "RUNNING" >nul
    if %errorlevel% == 0 (
        echo MongoDB is already running.
    ) else (
        echo Starting MongoDB service...
        net start MongoDB
        if %errorlevel% == 0 (
            echo MongoDB service started successfully.
        ) else (
            echo Failed to start MongoDB service. Please start it manually.
        )
    )
) else (
    echo MongoDB service not found. 
    echo Please install MongoDB or start it manually if using a different installation.
    echo.
    echo Alternative: You can use MongoDB Atlas (cloud) instead:
    echo 1. Create a free account at https://www.mongodb.com/atlas
    echo 2. Get your connection string
    echo 3. Create a .env file in the server folder with:
    echo    MONGODB_URI=your_connection_string_here
)

echo.
echo Press any key to continue...
pause >nul
