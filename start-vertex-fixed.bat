@echo off
title Vertex Education Management System - Fixed Version
color 0A

echo ========================================
echo Vertex Education Management System
echo Fixed Version - No More Refresh Issues
echo ========================================
echo.

echo Step 1: Setting up database...
call setup-database.bat

echo.
echo Step 2: Starting Backend Server...
cd server
start "Vertex Backend" cmd /k "npm run dev"
cd ..

echo Waiting for backend to initialize...
timeout /t 8 /nobreak >nul

echo.
echo Step 3: Starting Frontend Application...
cd client
start "Vertex Frontend" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo System Started Successfully!
echo ========================================
echo.
echo Backend: http://localhost:3000
echo Frontend: http://localhost:8080
echo.
echo FIXES APPLIED:
echo - Removed debug code causing refresh loops
echo - Fixed React Router future flags warnings
echo - Optimized authentication context
echo - Added MongoDB connection improvements
echo - Throttled API calls to prevent spam
echo.
echo Login Credentials:
echo Admin: admin@local / admin
echo Manager: manager@local / manager
echo Secretary: secretary@local / secretary
echo Teacher: teacher@local / teacher
echo.
echo Press any key to close this window...
pause >nul
