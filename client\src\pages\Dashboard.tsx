
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import MainLayout from "@/components/layout/MainLayout";
import WelcomeBar from "@/components/dashboard/WelcomeBar";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import RoleBasedDashboard from "@/components/dashboard/RoleBasedDashboard";
import { UserRole } from "@/types";

const Dashboard = () => {
  const navigate = useNavigate();
  const { user, isLoading: isAuthLoading, isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  });

  // Redirect if not authenticated
  useEffect(() => {
    console.log('Dashboard: Auth check - isAuthLoading:', isAuthLoading, 'isAuthenticated:', isAuthenticated);
    if (!isAuthLoading && !isAuthenticated) {
      console.log('Dashboard: Not authenticated, redirecting to login');
      navigate("/");
    }
  }, [isAuthLoading, isAuthenticated, navigate]);

  // Show loading state while checking authentication
  if (isAuthLoading) {
    console.log('Dashboard: Showing loading state');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, don't render dashboard
  if (!isAuthenticated) {
    console.log('Dashboard: Not authenticated, returning null');
    return null;
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        {/* Welcome message and stats summary */}
        <WelcomeBar />

        {/* Date range selector */}
        <div className="mb-6 flex justify-end">
          <DateRangeSelector onRangeChange={setDateRange} />
        </div>

        {/* Role-based dashboard content */}
        <RoleBasedDashboard
          userRole={user.role}
          dateRange={dateRange}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      </div>
    </MainLayout>
  );
};

export default Dashboard;
